{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "AnythingLLM Developer API", "description": "API endpoints that enable programmatic reading, writing, and updating of your AnythingLLM instance. UI supplied by Swagger.io."}, "servers": [{"url": "/api"}], "paths": {"/v1/auth": {"get": {"tags": ["Authentication"], "description": "Verify the attached Authentication header contains a valid API token.", "parameters": [], "responses": {"200": {"description": "Valid auth token was found.", "content": {"application/json": {"schema": {"type": "object", "example": {"authenticated": true}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}}}}, "/v1/admin/is-multi-user-mode": {"get": {"tags": ["Admin"], "description": "Check to see if the instance is in multi-user-mode first. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"isMultiUser": true}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}}}}, "/v1/admin/users": {"get": {"tags": ["Admin"], "description": "Check to see if the instance is in multi-user-mode first. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"users": [{"username": "sample-sam", "role": "default"}]}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/admin/users/new": {"post": {"tags": ["Admin"], "description": "Create a new user with username and password. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"user": {"id": 1, "username": "sample-sam", "role": "default"}, "error": null}}}}}, "400": {"description": "Bad Request"}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Key pair object that will define the new user to add to the system.", "required": true, "content": {"application/json": {"example": {"username": "sample-sam", "password": "hunter2", "role": "default | admin"}}}}}}, "/v1/admin/users/{id}": {"post": {"tags": ["Admin"], "description": "Update existing user settings. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "id of the user in the database."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Key pair object that will update the found user. All fields are optional and will not update unless specified.", "required": true, "content": {"application/json": {"example": {"username": "sample-sam", "password": "hunter2", "role": "default | admin", "suspended": 0}}}}}, "delete": {"tags": ["Admin"], "description": "Delete existing user by id. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "id of the user in the database."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/admin/invites": {"get": {"tags": ["Admin"], "description": "List all existing invitations to instance regardless of status. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"invites": [{"id": 1, "status": "pending", "code": "abc-123", "claimedBy": null}]}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/admin/invite/new": {"post": {"tags": ["Admin"], "description": "Create a new invite code for someone to use to register with instance. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"invite": {"id": 1, "status": "pending", "code": "abc-123"}, "error": null}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Request body for creation parameters of the invitation", "required": false, "content": {"application/json": {"example": {"workspaceIds": [1, 2, 45]}}}}}}, "/v1/admin/invite/{id}": {"delete": {"tags": ["Admin"], "description": "Deactivates (soft-delete) invite by id. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "id of the invite in the database."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/admin/workspaces/{workspaceId}/users": {"get": {"tags": ["Admin"], "description": "Retrieve a list of users with permissions to access the specified workspace.", "parameters": [{"name": "workspaceId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "id of the workspace."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"users": [{"userId": 1, "role": "admin"}, {"userId": 2, "role": "member"}]}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/admin/workspaces/{workspaceId}/update-users": {"post": {"tags": ["Admin"], "description": "Overwrite workspace permissions to only be accessible by the given user ids and admins. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [{"name": "workspaceId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "id of the workspace in the database."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Entire array of user ids who can access the workspace. All fields are optional and will not update unless specified.", "required": true, "content": {"application/json": {"example": {"userIds": [1, 2, 4, 12]}}}}, "deprecated": true}}, "/v1/admin/workspaces/{workspaceSlug}/manage-users": {"post": {"tags": ["Admin"], "description": "Set workspace permissions to be accessible by the given user ids and admins. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [{"name": "workspaceSlug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "slug of the workspace in the database"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null, "users": [{"userId": 1, "username": "main-admin", "role": "admin"}, {"userId": 2, "username": "sample-sam", "role": "default"}]}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Array of user ids who will be given access to the target workspace. <code>reset</code> will remove all existing users from the workspace and only add the new users - default <code>false</code>.", "required": true, "content": {"application/json": {"example": {"userIds": [1, 2, 4, 12], "reset": false}}}}}}, "/v1/admin/workspace-chats": {"post": {"tags": ["Admin"], "description": "All chats in the system ordered by most recent. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Page offset to show of workspace chats. All fields are optional and will not update unless specified.", "required": false, "content": {"application/json": {"example": {"offset": 2}}}}}}, "/v1/admin/preferences": {"post": {"tags": ["Admin"], "description": "Update multi-user preferences for instance. Methods are disabled until multi user mode is enabled via the UI.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "401": {"description": "Instance is not in Multi-User mode. Method denied"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Object with setting key and new value to set. All keys are optional and will not update unless specified.", "required": true, "content": {"application/json": {"example": {"support_email": "<EMAIL>"}}}}}}, "/v1/document/upload": {"post": {"tags": ["Documents"], "description": "Upload a new file to AnythingLLM to be parsed and prepared for embedding.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null, "documents": [{"location": "custom-documents/anythingllm.txt-6e8be64c-c162-4b43-9997-b068c0071e8b.json", "name": "anythingllm.txt-6e8be64c-c162-4b43-9997-b068c0071e8b.json", "url": "file://Users/<USER>/Documents/anything-llm/collector/hotdir/anythingllm.txt", "title": "anythingllm.txt", "docAuthor": "Unknown", "description": "Unknown", "docSource": "a text file uploaded by the user.", "chunkSource": "anythingllm.txt", "published": "1/16/2024, 3:07:00 PM", "wordCount": 93, "token_count_estimate": 115}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "File to be uploaded.", "required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "format": "binary", "description": "The file to upload"}, "addToWorkspaces": {"type": "string", "description": "comma-separated text-string of workspace slugs to embed the document into post-upload. eg: workspace1,workspace2"}}}}}}}}, "/v1/document/upload/{folderName}": {"post": {"tags": ["Documents"], "description": "Upload a new file to a specific folder in AnythingLLM to be parsed and prepared for embedding. If the folder does not exist, it will be created.", "parameters": [{"name": "folderName", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Target folder path (defaults to 'custom-documents' if not provided)", "example": "my-folder"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null, "documents": [{"location": "custom-documents/anythingllm.txt-6e8be64c-c162-4b43-9997-b068c0071e8b.json", "name": "anythingllm.txt-6e8be64c-c162-4b43-9997-b068c0071e8b.json", "url": "file://Users/<USER>/Documents/anything-llm/collector/hotdir/anythingllm.txt", "title": "anythingllm.txt", "docAuthor": "Unknown", "description": "Unknown", "docSource": "a text file uploaded by the user.", "chunkSource": "anythingllm.txt", "published": "1/16/2024, 3:07:00 PM", "wordCount": 93, "token_count_estimate": 115}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "example": {"success": false, "error": "Document processing API is not online. Document will not be processed automatically."}}}}}}, "requestBody": {"description": "File to be uploaded.", "required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "format": "binary", "description": "The file to upload"}, "addToWorkspaces": {"type": "string", "description": "comma-separated text-string of workspace slugs to embed the document into post-upload. eg: workspace1,workspace2"}}}}}}}}, "/v1/document/upload-link": {"post": {"tags": ["Documents"], "description": "Upload a valid URL for AnythingLLM to scrape and prepare for embedding. Optionally, specify a comma-separated list of workspace slugs to embed the document into post-upload.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null, "documents": [{"id": "c530dbe6-bff1-4b9e-b87f-710d539d20bc", "url": "file://useanything_com.html", "title": "useanything_com.html", "docAuthor": "no author found", "description": "No description found.", "docSource": "URL link uploaded by the user.", "chunkSource": "https:anythingllm.com.html", "published": "1/16/2024, 3:46:33 PM", "wordCount": 252, "pageContent": "AnythingLLM is the best....", "token_count_estimate": 447, "location": "custom-documents/url-useanything_com-c530dbe6-bff1-4b9e-b87f-710d539d20bc.json"}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Link of web address to be scraped and optionally a comma-separated list of workspace slugs to embed the document into post-upload.", "required": true, "content": {"application/json": {"schema": {"type": "object", "example": {"link": "https://anythingllm.com", "addToWorkspaces": "workspace1,workspace2", "scraperHeaders": {"Authorization": "Bearer token123", "My-Custom-Header": "value"}}}}}}}}, "/v1/document/raw-text": {"post": {"tags": ["Documents"], "description": "Upload a file by specifying its raw text content and metadata values without having to upload a file.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null, "documents": [{"id": "c530dbe6-bff1-4b9e-b87f-710d539d20bc", "url": "file://my-document.txt", "title": "hello-world.txt", "docAuthor": "no author found", "description": "No description found.", "docSource": "My custom description set during upload", "chunkSource": "no chunk source specified", "published": "1/16/2024, 3:46:33 PM", "wordCount": 252, "pageContent": "AnythingLLM is the best....", "token_count_estimate": 447, "location": "custom-documents/raw-my-doc-text-c530dbe6-bff1-4b9e-b87f-710d539d20bc.json"}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "422": {"description": "Unprocessable Entity"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Text content and metadata of the file to be saved to the system. Use metadata-schema endpoint to get the possible metadata keys", "required": true, "content": {"application/json": {"schema": {"type": "object", "example": {"textContent": "This is the raw text that will be saved as a document in AnythingLLM.", "addToWorkspaces": "workspace1,workspace2", "metadata": {"title": "This key is required. See in /server/endpoints/api/document/index.js:287", "keyOne": "valueOne", "keyTwo": "valueTwo", "etc": "etc"}}}}}}}}, "/v1/documents": {"get": {"tags": ["Documents"], "description": "List of all locally-stored documents in instance", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"localFiles": {"name": "documents", "type": "folder", "items": [{"name": "my-stored-document.json", "type": "file", "id": "bb07c334-4dab-4419-9462-9d00065a49a1", "url": "file://my-stored-document.txt", "title": "my-stored-document.txt", "cached": false}]}}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/documents/folder/{folderName}": {"get": {"tags": ["Documents"], "description": "Get all documents stored in a specific folder.", "parameters": [{"name": "folderName", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Name of the folder to retrieve documents from"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"folder": "custom-documents", "documents": [{"name": "document1.json", "type": "file", "cached": false, "pinnedWorkspaces": [], "watched": false, "more": "data"}, {"name": "document2.json", "type": "file", "cached": false, "pinnedWorkspaces": [], "watched": false, "more": "data"}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/document/accepted-file-types": {"get": {"tags": ["Documents"], "description": "Check available filetypes and MIMEs that can be uploaded.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"types": {"application/mbox": [".mbox"], "application/pdf": [".pdf"], "application/vnd.oasis.opendocument.text": [".odt"], "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"], "text/plain": [".txt", ".md"]}}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/document/metadata-schema": {"get": {"tags": ["Documents"], "description": "Get the known available metadata schema for when doing a raw-text upload and the acceptable type of value for each key.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"schema": {"keyOne": "string | number | nullable", "keyTwo": "string | number | nullable", "specialKey": "number", "title": "string"}}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/document/{docName}": {"get": {"tags": ["Documents"], "description": "Get a single document by its unique AnythingLLM document name", "parameters": [{"name": "doc<PERSON>ame", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique document name to find (name in /documents)"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"localFiles": {"name": "documents", "type": "folder", "items": [{"name": "my-stored-document.txt-uuid1234.json", "type": "file", "id": "bb07c334-4dab-4419-9462-9d00065a49a1", "url": "file://my-stored-document.txt", "title": "my-stored-document.txt", "cached": false}]}}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/document/create-folder": {"post": {"tags": ["Documents"], "description": "Create a new folder inside the documents storage directory.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "message": null}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Name of the folder to create.", "required": true, "content": {"application/json": {"schema": {"type": "string", "example": {"name": "new-folder"}}}}}}}, "/v1/document/remove-folder": {"delete": {"tags": ["Documents"], "description": "Remove a folder and all its contents from the documents storage directory.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "message": "Folder removed successfully"}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Name of the folder to remove.", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "my-folder"}}}}}}}}, "/v1/document/move-files": {"post": {"tags": ["Documents"], "description": "Move files within the documents storage directory.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "message": null}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Array of objects containing source and destination paths of files to move.", "required": true, "content": {"application/json": {"schema": {"type": "object", "example": {"files": [{"from": "custom-documents/file.txt-fc4beeeb-e436-454d-8bb4-e5b8979cb48f.json", "to": "folder/file.txt-fc4beeeb-e436-454d-8bb4-e5b8979cb48f.json"}]}}}}}}}, "/v1/workspace/new": {"post": {"tags": ["Workspaces"], "description": "Create a new workspace", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"workspace": {"id": 79, "name": "Sample workspace", "slug": "sample-workspace", "createdAt": "2023-08-17 00:45:03", "openAiTemp": null, "lastUpdatedAt": "2023-08-17 00:45:03", "openAiHistory": 20, "openAiPrompt": null}, "message": "Workspace created"}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "JSON object containing workspace configuration.", "required": true, "content": {"application/json": {"example": {"name": "My New Workspace", "similarityThreshold": 0.7, "openAiTemp": 0.7, "openAiHistory": 20, "openAiPrompt": "Custom prompt for responses", "queryRefusalResponse": "Custom refusal message", "chatMode": "chat", "topN": 4}}}}}}, "/v1/workspaces": {"get": {"tags": ["Workspaces"], "description": "List all current workspaces", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"workspaces": [{"id": 79, "name": "Sample workspace", "slug": "sample-workspace", "createdAt": "2023-08-17 00:45:03", "openAiTemp": null, "lastUpdatedAt": "2023-08-17 00:45:03", "openAiHistory": 20, "openAiPrompt": null, "threads": []}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/workspace/{slug}": {"get": {"tags": ["Workspaces"], "description": "Get a workspace by its unique slug.", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace to find"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"workspace": [{"id": 79, "name": "My workspace", "slug": "my-workspace-123", "createdAt": "2023-08-17 00:45:03", "openAiTemp": null, "lastUpdatedAt": "2023-08-17 00:45:03", "openAiHistory": 20, "openAiPrompt": null, "documents": [], "threads": []}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Workspaces"], "description": "Deletes a workspace by its slug.", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace to delete"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/workspace/{slug}/update": {"post": {"tags": ["Workspaces"], "description": "Update workspace settings by its unique slug.", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace to find"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"workspace": {"id": 79, "name": "My workspace", "slug": "my-workspace-123", "createdAt": "2023-08-17 00:45:03", "openAiTemp": null, "lastUpdatedAt": "2023-08-17 00:45:03", "openAiHistory": 20, "openAiPrompt": null, "documents": []}, "message": null}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "JSON object containing new settings to update a workspace. All keys are optional and will not update unless provided", "required": true, "content": {"application/json": {"example": {"name": "Updated Workspace Name", "openAiTemp": 0.2, "openAiHistory": 20, "openAiPrompt": "Respond to all inquires and questions in binary - do not respond in any other format."}}}}}}, "/v1/workspace/{slug}/chats": {"get": {"tags": ["Workspaces"], "description": "Get a workspaces chats regardless of user by its unique slug.", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace to find"}, {"name": "apiSessionId", "in": "query", "description": "Optional apiSessionId to filter by", "required": false, "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "Optional number of chat messages to return (default: 100)", "required": false, "schema": {"type": "integer"}}, {"name": "orderBy", "in": "query", "description": "Optional order of chat messages (asc or desc)", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"history": [{"role": "user", "content": "What is AnythingLLM?", "sentAt": 1692851630}, {"role": "assistant", "content": "AnythingLLM is a platform that allows you to convert notes, PDFs, and other source materials into a chatbot. It ensures privacy, cites its answers, and allows multiple people to interact with the same documents simultaneously. It is particularly useful for businesses to enhance the visibility and readability of various written communications such as SOPs, contracts, and sales calls. You can try it out with a free trial to see if it meets your business needs.", "sources": [{"source": "object about source document and snippets used"}]}]}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/workspace/{slug}/update-embeddings": {"post": {"tags": ["Workspaces"], "description": "Add or remove documents from a workspace by its unique slug.", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace to find"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"workspace": {"id": 79, "name": "My workspace", "slug": "my-workspace-123", "createdAt": "2023-08-17 00:45:03", "openAiTemp": null, "lastUpdatedAt": "2023-08-17 00:45:03", "openAiHistory": 20, "openAiPrompt": null, "documents": []}, "message": null}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "JSON object of additions and removals of documents to add to update a workspace. The value should be the folder + filename with the exclusions of the top-level documents path.", "required": true, "content": {"application/json": {"example": {"adds": ["custom-documents/my-pdf.pdf-hash.json"], "deletes": ["custom-documents/anythingllm.txt-hash.json"]}}}}}}, "/v1/workspace/{slug}/update-pin": {"post": {"tags": ["Workspaces"], "description": "Add or remove pin from a document in a workspace by its unique slug.", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace to find"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"message": "Pin status updated successfully"}}}}}, "403": {"description": "Forbidden"}, "404": {"description": "Document not found"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "JSON object with the document path and pin status to update.", "required": true, "content": {"application/json": {"example": {"docPath": "custom-documents/my-pdf.pdf-hash.json", "pinStatus": true}}}}}}, "/v1/workspace/{slug}/chat": {"post": {"tags": ["Workspaces"], "description": "Execute a chat with a workspace", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"id": "chat-uuid", "type": "abort | textResponse", "textResponse": "Response to your query", "sources": [{"title": "anythingllm.txt", "chunk": "This is a context chunk used in the answer of the prompt by the LLM,"}], "close": true, "error": "null | text string of the failure mode."}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Send a prompt to the workspace and the type of conversation (query or chat).<br/><b>Query:</b> Will not use LLM unless there are relevant sources from vectorDB & does not recall chat history.<br/><b>Chat:</b> Uses LLM general knowledge w/custom embeddings to produce output, uses rolling chat history.", "required": true, "content": {"application/json": {"example": {"message": "What is AnythingLLM?", "mode": "query | chat", "sessionId": "identifier-to-partition-chats-by-external-id", "attachments": [{"name": "image.png", "mime": "image/png", "contentString": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."}], "reset": false}}}}}}, "/v1/workspace/{slug}/stream-chat": {"post": {"tags": ["Workspaces"], "description": "Execute a streamable chat with a workspace", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"text/event-stream": {"schema": {"type": "array", "items": {"type": "string"}, "example": [{"id": "uuid-123", "type": "abort | textResponseChunk", "textResponse": "First chunk", "sources": [], "close": false, "error": "null | text string of the failure mode."}, {"id": "uuid-123", "type": "abort | textResponseChunk", "textResponse": "chunk two", "sources": [], "close": false, "error": "null | text string of the failure mode."}, {"id": "uuid-123", "type": "abort | textResponseChunk", "textResponse": "final chunk of LLM output!", "sources": [{"title": "anythingllm.txt", "chunk": "This is a context chunk used in the answer of the prompt by the LLM. This will only return in the final chunk."}], "close": true, "error": "null | text string of the failure mode."}]}}}, "description": "OK"}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}}, "requestBody": {"description": "Send a prompt to the workspace and the type of conversation (query or chat).<br/><b>Query:</b> Will not use LLM unless there are relevant sources from vectorDB & does not recall chat history.<br/><b>Chat:</b> Uses LLM general knowledge w/custom embeddings to produce output, uses rolling chat history.", "required": true, "content": {"application/json": {"example": {"message": "What is AnythingLLM?", "mode": "query | chat", "sessionId": "identifier-to-partition-chats-by-external-id", "attachments": [{"name": "image.png", "mime": "image/png", "contentString": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."}], "reset": false}}}}}}, "/v1/workspace/{slug}/vector-search": {"post": {"tags": ["Workspaces"], "description": "Perform a vector similarity search in a workspace", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace to search in"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"results": [{"id": "5a6bee0a-306c-47fc-942b-8ab9bf3899c4", "text": "Document chunk content...", "metadata": {"url": "file://document.txt", "title": "document.txt", "author": "no author specified", "description": "no description found", "docSource": "post:123456", "chunkSource": "document.txt", "published": "12/1/2024, 11:39:39 AM", "wordCount": 8, "tokenCount": 9}, "distance": 0.541887640953064, "score": 0.45811235904693604}]}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Query to perform vector search with and optional parameters", "required": true, "content": {"application/json": {"example": {"query": "What is the meaning of life?", "topN": 4, "scoreThreshold": 0.75}}}}}}, "/v1/system/env-dump": {"get": {"tags": ["System Settings"], "description": "Dump all settings to file storage", "parameters": [], "responses": {"200": {"description": "OK"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/system": {"get": {"tags": ["System Settings"], "description": "Get all current system settings that are defined.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"settings": {"VectorDB": "pinecone", "PineConeKey": true, "PineConeIndex": "my-pinecone-index", "LLMProvider": "azure", "[KEY_NAME]": "KEY_VALUE"}}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/system/vector-count": {"get": {"tags": ["System Settings"], "description": "Number of all vectors in connected vector database", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"vectorCount": 5450}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/system/update-env": {"post": {"tags": ["System Settings"], "description": "Update a system setting or preference.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"newValues": {"[ENV_KEY]": "Value"}, "error": "error goes here, otherwise null"}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Key pair object that matches a valid setting and value. Get keys from GET /v1/system or refer to codebase.", "required": true, "content": {"application/json": {"example": {"VectorDB": "lancedb", "AnotherKey": "updatedValue"}}}}}}, "/v1/system/export-chats": {"get": {"tags": ["System Settings"], "description": "Export all of the chats from the system in a known format. Output depends on the type sent. Will be send with the correct header for the output.", "parameters": [{"name": "type", "in": "query", "description": "Export format jsonl, json, csv, jsonAlpaca", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": [{"role": "user", "content": "What is Anythingl<PERSON>?"}, {"role": "assistant", "content": "AnythingLLM is a knowledge graph and vector database management system built using NodeJS express server. It provides an interface for handling all interactions, including vectorDB management and LLM (Language Model) interactions."}]}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/system/remove-documents": {"delete": {"tags": ["System Settings"], "description": "Permanently remove documents from the system.", "parameters": [], "responses": {"200": {"description": "Documents removed successfully.", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "message": "Documents removed successfully"}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Array of document names to be removed permanently.", "required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"names": {"type": "array", "items": {"type": "string"}, "example": ["custom-documents/file.txt-fc4beeeb-e436-454d-8bb4-e5b8979cb48f.json"]}}}}}}}}, "/v1/workspace/{slug}/thread/new": {"post": {"tags": ["Workspace Threads"], "description": "Create a new workspace thread", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"thread": {"id": 1, "name": "<PERSON><PERSON><PERSON>", "slug": "thread-uuid", "user_id": 1, "workspace_id": 1}, "message": null}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Optional userId associated with the thread, thread slug and thread name", "required": false, "content": {"application/json": {"example": {"userId": 1, "name": "Name", "slug": "thread-slug"}}}}}}, "/v1/workspace/{slug}/thread/{threadSlug}/update": {"post": {"tags": ["Workspace Threads"], "description": "Update thread name by its unique slug.", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace"}, {"name": "threadSlug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of thread"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"thread": {"id": 1, "name": "Updated Thread Name", "slug": "thread-uuid", "user_id": 1, "workspace_id": 1}, "message": null}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "JSON object containing new name to update the thread.", "required": true, "content": {"application/json": {"example": {"name": "Updated Thread Name"}}}}}}, "/v1/workspace/{slug}/thread/{threadSlug}": {"delete": {"tags": ["Workspace Threads"], "description": "Delete a workspace thread", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace"}, {"name": "threadSlug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of thread"}], "responses": {"200": {"description": "Thread deleted successfully"}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/workspace/{slug}/thread/{threadSlug}/chats": {"get": {"tags": ["Workspace Threads"], "description": "Get chats for a workspace thread", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace"}, {"name": "threadSlug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of thread"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"history": [{"role": "user", "content": "What is AnythingLLM?", "sentAt": 1692851630}, {"role": "assistant", "content": "AnythingLLM is a platform that allows you to convert notes, PDFs, and other source materials into a chatbot. It ensures privacy, cites its answers, and allows multiple people to interact with the same documents simultaneously. It is particularly useful for businesses to enhance the visibility and readability of various written communications such as SOPs, contracts, and sales calls. You can try it out with a free trial to see if it meets your business needs.", "sources": [{"source": "object about source document and snippets used"}]}]}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/workspace/{slug}/thread/{threadSlug}/chat": {"post": {"tags": ["Workspace Threads"], "description": "Chat with a workspace thread", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace"}, {"name": "threadSlug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of thread"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"id": "chat-uuid", "type": "abort | textResponse", "textResponse": "Response to your query", "sources": [{"title": "anythingllm.txt", "chunk": "This is a context chunk used in the answer of the prompt by the LLM."}], "close": true, "error": "null | text string of the failure mode."}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Send a prompt to the workspace thread and the type of conversation (query or chat).", "required": true, "content": {"application/json": {"example": {"message": "What is AnythingLLM?", "mode": "query | chat", "userId": 1, "attachments": [{"name": "image.png", "mime": "image/png", "contentString": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."}], "reset": false}}}}}}, "/v1/workspace/{slug}/thread/{threadSlug}/stream-chat": {"post": {"tags": ["Workspace Threads"], "description": "Stream chat with a workspace thread", "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of workspace"}, {"name": "threadSlug", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Unique slug of thread"}], "responses": {"200": {"content": {"text/event-stream": {"schema": {"type": "array", "items": {"type": "string"}, "example": [{"id": "uuid-123", "type": "abort | textResponseChunk", "textResponse": "First chunk", "sources": [], "close": false, "error": "null | text string of the failure mode."}, {"id": "uuid-123", "type": "abort | textResponseChunk", "textResponse": "chunk two", "sources": [], "close": false, "error": "null | text string of the failure mode."}, {"id": "uuid-123", "type": "abort | textResponseChunk", "textResponse": "final chunk of LLM output!", "sources": [{"title": "anythingllm.txt", "chunk": "This is a context chunk used in the answer of the prompt by the LLM. This will only return in the final chunk."}], "close": true, "error": "null | text string of the failure mode."}]}}}, "description": "OK"}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}}, "requestBody": {"description": "Send a prompt to the workspace thread and the type of conversation (query or chat).", "required": true, "content": {"application/json": {"example": {"message": "What is AnythingLLM?", "mode": "query | chat", "userId": 1, "attachments": [{"name": "image.png", "mime": "image/png", "contentString": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."}], "reset": false}}}}}}, "/v1/users": {"get": {"tags": ["User Management"], "description": "List all users", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"users": [{"id": 1, "username": "john_doe", "role": "admin"}, {"id": 2, "username": "jane_smith", "role": "default"}]}}}}}, "401": {"description": "Instance is not in Multi-User mode. Permission denied."}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/users/{id}/issue-auth-token": {"get": {"tags": ["User Management"], "description": "Issue a temporary auth token for a user", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the user to issue a temporary auth token for"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"token": "1234567890", "loginPath": "/sso/simple?token=1234567890"}}}}}, "401": {"description": "Instance is not in Multi-User mode. Permission denied."}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/openai/models": {"get": {"tags": ["OpenAI Compatible Endpoints"], "description": "Get all available \"models\" which are workspaces you can use for chatting.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"models": [{"name": "Sample workspace", "model": "sample-workspace", "llm": {"provider": "ollama", "model": "llama3:8b"}}, {"name": "Second workspace", "model": "workspace-2", "llm": {"provider": "openai", "model": "gpt-3.5-turbo"}}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/openai/chat/completions": {"post": {"tags": ["OpenAI Compatible Endpoints"], "description": "Execute a chat with a workspace with OpenAI compatibility. Supports streaming as well. Model must be a workspace slug from /models.", "parameters": [], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "Send a prompt to the workspace with full use of documents as if sending a chat in AnythingLLM. Only supports some values of OpenAI API. See example below.", "required": true, "content": {"application/json": {"example": {"messages": [{"role": "system", "content": "You are a helpful assistant"}, {"role": "user", "content": "What is AnythingLLM?"}, {"role": "assistant", "content": "AnythingLLM is...."}, {"role": "user", "content": "Follow up question..."}], "model": "sample-workspace", "stream": true, "temperature": 0.7}}}}}}, "/v1/openai/embeddings": {"post": {"tags": ["OpenAI Compatible Endpoints"], "description": "Get the embeddings of any arbitrary text string. This will use the embedder provider set in the system. Please ensure the token length of each string fits within the context of your embedder model.", "parameters": [], "responses": {"200": {"description": "OK"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "The input string(s) to be embedded. If the text is too long for the embedder model context, it will fail to embed. The vector and associated chunk metadata will be returned in the array order provided", "required": true, "content": {"application/json": {"example": {"input": ["This is my first string to embed", "This is my second string to embed"], "model": null}}}}}}, "/v1/openai/vector_stores": {"get": {"tags": ["OpenAI Compatible Endpoints"], "description": "List all the vector database collections connected to AnythingLLM. These are essentially workspaces but return their unique vector db identifier - this is the same as the workspace slug.", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"data": [{"id": "slug-here", "object": "vector_store", "name": "My workspace", "file_counts": {"total": 3}, "provider": "LanceDB"}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/embed": {"get": {"tags": ["Embed"], "description": "List all active embeds", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"embeds": [{"id": 1, "uuid": "embed-uuid-1", "enabled": true, "chat_mode": "query", "createdAt": "2023-04-01T12:00:00Z", "workspace": {"id": 1, "name": "Workspace 1"}, "chat_count": 10}, {"id": 2, "uuid": "embed-uuid-2", "enabled": false, "chat_mode": "chat", "createdAt": "2023-04-02T14:30:00Z", "workspace": {"id": 1, "name": "Workspace 1"}, "chat_count": 10}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "500": {"description": "Internal Server Error"}}}}, "/v1/embed/{embedUuid}/chats": {"get": {"tags": ["Embed"], "description": "Get all chats for a specific embed", "parameters": [{"name": "embedUuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "UUID of the embed"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"chats": [{"id": 1, "session_id": "session-uuid-1", "prompt": "Hello", "response": "Hi there!", "createdAt": "2023-04-01T12:00:00Z"}, {"id": 2, "session_id": "session-uuid-2", "prompt": "How are you?", "response": "I'm doing well, thank you!", "createdAt": "2023-04-02T14:30:00Z"}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Embed not found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/embed/{embedUuid}/chats/{sessionUuid}": {"get": {"tags": ["Embed"], "description": "Get chats for a specific embed and session", "parameters": [{"name": "embedUuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "UUID of the embed"}, {"name": "sessionUuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "UUID of the session"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"chats": [{"id": 1, "prompt": "Hello", "response": "Hi there!", "createdAt": "2023-04-01T12:00:00Z"}]}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Embed or session not found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/embed/new": {"post": {"tags": ["Embed"], "description": "Create a new embed configuration", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"embed": {"id": 1, "uuid": "embed-uuid-1", "enabled": true, "chat_mode": "chat", "allowlist_domains": ["example.com"], "allow_model_override": false, "allow_temperature_override": false, "allow_prompt_override": false, "max_chats_per_day": 100, "max_chats_per_session": 10, "createdAt": "2023-04-01T12:00:00Z", "workspace_slug": "workspace-slug-1"}, "error": null}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Workspace not found"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "JSON object containing embed configuration details", "required": true, "content": {"application/json": {"schema": {"type": "object", "example": {"workspace_slug": "workspace-slug-1", "chat_mode": "chat", "allowlist_domains": ["example.com"], "allow_model_override": false, "allow_temperature_override": false, "allow_prompt_override": false, "max_chats_per_day": 100, "max_chats_per_session": 10}}}}}}}, "/v1/embed/{embedUuid}": {"post": {"tags": ["Embed"], "description": "Update an existing embed configuration", "parameters": [{"name": "embedUuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "UUID of the embed to update"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Embed not found"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"description": "JSON object containing embed configuration updates", "required": true, "content": {"application/json": {"schema": {"type": "object", "example": {"enabled": true, "chat_mode": "chat", "allowlist_domains": ["example.com"], "allow_model_override": false, "allow_temperature_override": false, "allow_prompt_override": false, "max_chats_per_day": 100, "max_chats_per_session": 10}}}}}}, "delete": {"tags": ["Embed"], "description": "Delete an existing embed configuration", "parameters": [{"name": "embedUuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "UUID of the embed to delete"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "example": {"success": true, "error": null}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/InvalidAPIKey"}}}}, "404": {"description": "Embed not found"}, "500": {"description": "Internal Server Error"}}}}}, "components": {"schemas": {"InvalidAPIKey": {"type": "object", "properties": {"message": {"type": "string", "example": "Invalid API Key"}}, "xml": {"name": "InvalidAPIKey"}}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"BearerAuth": []}]}