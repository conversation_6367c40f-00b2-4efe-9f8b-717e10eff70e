# This GitHub action is for checking the versions of the packages in the project.
# Any package that is present in both the `server` and `collector` package.json file
# is checked to ensure that they are the same version.
name: Check package versions

concurrency:
  group: build-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - "server/package.json"
      - "collector/package.json"

jobs:
  run-script:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Run verifyPackageVersions.mjs script
        run: |
          cd extras/scripts
          node verifyPackageVersions.mjs

      - name: Fail job on error
        if: failure()
        run: exit 1
