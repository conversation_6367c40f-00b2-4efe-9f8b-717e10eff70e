generator client {
  provider = "prisma-client-js"
}

// Uncomment the following lines and comment out the SQLite datasource block above to use PostgreSQL
// Make sure to set the correct DATABASE_URL in your .env file
// After swapping run `yarn prisma:setup` from the root directory to migrate the database
//
// datasource db {
//   provider = "postgresql"
//   url      = env("DATABASE_URL")
// }
datasource db {
  provider = "sqlite"
  url      = "file:../storage/anythingllm.db"
}

model api_keys {
  id            Int      @id @default(autoincrement())
  secret        String?  @unique
  createdBy     Int?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
}

model workspace_documents {
  id                   Int                   @id @default(autoincrement())
  docId                String                @unique
  filename             String
  docpath              String
  workspaceId          Int
  metadata             String?
  pinned               Boolean?              @default(false)
  watched              Boolean?              @default(false)
  createdAt            DateTime              @default(now())
  lastUpdatedAt        DateTime              @default(now())
  workspace            workspaces            @relation(fields: [workspaceId], references: [id])
  document_sync_queues document_sync_queues?
}

model invites {
  id            Int      @id @default(autoincrement())
  code          String   @unique
  status        String   @default("pending")
  claimedBy     Int?
  workspaceIds  String?
  createdAt     DateTime @default(now())
  createdBy     Int
  lastUpdatedAt DateTime @default(now())
}

model system_settings {
  id            Int      @id @default(autoincrement())
  label         String   @unique
  value         String?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
}

model users {
  id                          Int                           @id @default(autoincrement())
  username                    String?                       @unique
  password                    String
  pfpFilename                 String?
  role                        String                        @default("default")
  suspended                   Int                           @default(0)
  seen_recovery_codes         Boolean?                      @default(false)
  createdAt                   DateTime                      @default(now())
  lastUpdatedAt               DateTime                      @default(now())
  dailyMessageLimit           Int?
  bio                         String?                       @default("")
  workspace_chats             workspace_chats[]
  workspace_users             workspace_users[]
  embed_configs               embed_configs[]
  embed_chats                 embed_chats[]
  threads                     workspace_threads[]
  recovery_codes              recovery_codes[]
  password_reset_tokens       password_reset_tokens[]
  workspace_agent_invocations workspace_agent_invocations[]
  slash_command_presets       slash_command_presets[]
  browser_extension_api_keys  browser_extension_api_keys[]
  temporary_auth_tokens       temporary_auth_tokens[]
  system_prompt_variables     system_prompt_variables[]
  prompt_history              prompt_history[]
}

model recovery_codes {
  id        Int      @id @default(autoincrement())
  user_id   Int
  code_hash String
  createdAt DateTime @default(now())
  user      users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model password_reset_tokens {
  id        Int      @id @default(autoincrement())
  user_id   Int
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      users    @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model document_vectors {
  id            Int      @id @default(autoincrement())
  docId         String
  vectorId      String
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
}

model welcome_messages {
  id         Int      @id @default(autoincrement())
  user       String
  response   String
  orderIndex Int?
  createdAt  DateTime @default(now())
}

model workspaces {
  id                           Int                            @id @default(autoincrement())
  name                         String
  slug                         String                         @unique
  vectorTag                    String?
  createdAt                    DateTime                       @default(now())
  openAiTemp                   Float?
  openAiHistory                Int                            @default(20)
  lastUpdatedAt                DateTime                       @default(now())
  openAiPrompt                 String?
  similarityThreshold          Float?                         @default(0.25)
  chatProvider                 String?
  chatModel                    String?
  topN                         Int?                           @default(4)
  chatMode                     String?                        @default("chat")
  pfpFilename                  String?
  agentProvider                String?
  agentModel                   String?
  queryRefusalResponse         String?
  vectorSearchMode             String?                        @default("default")
  workspace_users              workspace_users[]
  documents                    workspace_documents[]
  workspace_suggested_messages workspace_suggested_messages[]
  embed_configs                embed_configs[]
  threads                      workspace_threads[]
  workspace_agent_invocations  workspace_agent_invocations[]
  prompt_history               prompt_history[]
}

model workspace_threads {
  id            Int        @id @default(autoincrement())
  name          String
  slug          String     @unique
  workspace_id  Int
  user_id       Int?
  createdAt     DateTime   @default(now())
  lastUpdatedAt DateTime   @default(now())
  workspace     workspaces @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  user          users?     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([workspace_id])
  @@index([user_id])
}

model workspace_suggested_messages {
  id            Int        @id @default(autoincrement())
  workspaceId   Int
  heading       String
  message       String
  createdAt     DateTime   @default(now())
  lastUpdatedAt DateTime   @default(now())
  workspace     workspaces @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@index([workspaceId])
}

model workspace_chats {
  id             Int      @id @default(autoincrement())
  workspaceId    Int
  prompt         String
  response       String
  include        Boolean  @default(true)
  user_id        Int?
  thread_id      Int? // No relation to prevent whole table migration
  api_session_id String? // String identifier for only the dev API to parition chats in any mode.
  createdAt      DateTime @default(now())
  lastUpdatedAt  DateTime @default(now())
  feedbackScore  Boolean?
  users          users?   @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model workspace_agent_invocations {
  id            Int        @id @default(autoincrement())
  uuid          String     @unique
  prompt        String // Contains agent invocation to parse + option additional text for seed.
  closed        Boolean    @default(false)
  user_id       Int?
  thread_id     Int? // No relation to prevent whole table migration
  workspace_id  Int
  createdAt     DateTime   @default(now())
  lastUpdatedAt DateTime   @default(now())
  user          users?     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
  workspace     workspaces @relation(fields: [workspace_id], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([uuid])
}

model workspace_users {
  id            Int        @id @default(autoincrement())
  user_id       Int
  workspace_id  Int
  createdAt     DateTime   @default(now())
  lastUpdatedAt DateTime   @default(now())
  workspaces    workspaces @relation(fields: [workspace_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
  users         users      @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model cache_data {
  id            Int       @id @default(autoincrement())
  name          String
  data          String
  belongsTo     String?
  byId          Int?
  expiresAt     DateTime?
  createdAt     DateTime  @default(now())
  lastUpdatedAt DateTime  @default(now())
}

model embed_configs {
  id                         Int           @id @default(autoincrement())
  uuid                       String        @unique
  enabled                    Boolean       @default(false)
  chat_mode                  String        @default("query")
  allowlist_domains          String?
  allow_model_override       Boolean       @default(false)
  allow_temperature_override Boolean       @default(false)
  allow_prompt_override      Boolean       @default(false)
  max_chats_per_day          Int?
  max_chats_per_session      Int?
  workspace_id               Int
  createdBy                  Int?
  usersId                    Int?
  createdAt                  DateTime      @default(now())
  workspace                  workspaces    @relation(fields: [workspace_id], references: [id], onDelete: Cascade)
  embed_chats                embed_chats[]
  users                      users?        @relation(fields: [usersId], references: [id])
}

model embed_chats {
  id                     Int           @id @default(autoincrement())
  prompt                 String
  response               String
  session_id             String
  include                Boolean       @default(true)
  connection_information String?
  embed_id               Int
  usersId                Int?
  createdAt              DateTime      @default(now())
  embed_config           embed_configs @relation(fields: [embed_id], references: [id], onDelete: Cascade)
  users                  users?        @relation(fields: [usersId], references: [id])
}

model event_logs {
  id         Int      @id @default(autoincrement())
  event      String
  metadata   String?
  userId     Int?
  occurredAt DateTime @default(now())

  @@index([event])
}

model slash_command_presets {
  id            Int      @id @default(autoincrement())
  command       String
  prompt        String
  description   String
  uid           Int      @default(0) // 0 is null user
  userId        Int?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @default(now())
  user          users?   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([uid, command])
}

model document_sync_queues {
  id             Int                        @id @default(autoincrement())
  staleAfterMs   Int                        @default(604800000) // 7 days
  nextSyncAt     DateTime
  createdAt      DateTime                   @default(now())
  lastSyncedAt   DateTime                   @default(now())
  workspaceDocId Int                        @unique
  workspaceDoc   workspace_documents?       @relation(fields: [workspaceDocId], references: [id], onDelete: Cascade)
  runs           document_sync_executions[]
}

model document_sync_executions {
  id        Int                  @id @default(autoincrement())
  queueId   Int
  status    String               @default("unknown")
  result    String?
  createdAt DateTime             @default(now())
  queue     document_sync_queues @relation(fields: [queueId], references: [id], onDelete: Cascade)
}

model browser_extension_api_keys {
  id            Int      @id @default(autoincrement())
  key           String   @unique
  user_id       Int?
  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @updatedAt
  user          users?   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model temporary_auth_tokens {
  id        Int      @id @default(autoincrement())
  token     String   @unique
  userId    Int
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([userId])
}

model system_prompt_variables {
  id          Int      @id @default(autoincrement())
  key         String   @unique
  value       String?
  description String?
  type        String   @default("system") // system, user, dynamic
  userId      Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        users?   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model prompt_history {
  id          Int        @id @default(autoincrement())
  workspace   workspaces @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId Int
  prompt      String
  modifiedBy  Int?
  modifiedAt  DateTime   @default(now())
  user        users?     @relation(fields: [modifiedBy], references: [id])

  @@index([workspaceId])
}
