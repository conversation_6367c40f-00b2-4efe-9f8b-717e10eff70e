/*!
  Theme: GitHub Dark
  Description: Dark theme as seen on github.com
  Author: github.com
  Maintainer: @Hirse
  Updated: 2021-05-15

  Outdated base version: https://github.com/primer/github-syntax-dark
  Current colors taken from GitHub's CSS
*/

.github-dark.hljs {
  color: #c9d1d9;
  background: #0d1117;
}

.github-dark .hljs-doctag,
.github-dark .hljs-keyword,
.github-dark .hljs-meta .hljs-keyword,
.github-dark .hljs-template-tag,
.github-dark .hljs-template-variable,
.github-dark .hljs-type,
.github-dark .hljs-variable.language_ {
  /* prettylights-syntax-keyword */
  color: #ff7b72;
}

.github-dark .hljs-title,
.github-dark .hljs-title.class_,
.github-dark .hljs-title.class_.inherited__,
.github-dark .hljs-title.function_ {
  /* prettylights-syntax-entity */
  color: #d2a8ff;
}

.github-dark .hljs-attr,
.github-dark .hljs-attribute,
.github-dark .hljs-literal,
.github-dark .hljs-meta,
.github-dark .hljs-number,
.github-dark .hljs-operator,
.github-dark .hljs-variable,
.github-dark .hljs-selector-attr,
.github-dark .hljs-selector-class,
.github-dark .hljs-selector-id {
  /* prettylights-syntax-constant */
  color: #79c0ff;
}

.github-dark .hljs-regexp,
.github-dark .hljs-string,
.github-dark .hljs-meta .hljs-string {
  /* prettylights-syntax-string */
  color: #a5d6ff;
}

.github-dark .hljs-built_in,
.github-dark .hljs-symbol {
  /* prettylights-syntax-variable */
  color: #ffa657;
}

.github-dark .hljs-comment,
.github-dark .hljs-code,
.github-dark .hljs-formula {
  /* prettylights-syntax-comment */
  color: #8b949e;
}

.github-dark .hljs-name,
.github-dark .hljs-quote,
.github-dark .hljs-selector-tag,
.github-dark .hljs-selector-pseudo {
  /* prettylights-syntax-entity-tag */
  color: #7ee787;
}

.github-dark .hljs-subst {
  /* prettylights-syntax-storage-modifier-import */
  color: #c9d1d9;
}

.github-dark .hljs-section {
  /* prettylights-syntax-markup-heading */
  color: #1f6feb;
  font-weight: bold;
}

.github-dark .hljs-bullet {
  /* prettylights-syntax-markup-list */
  color: #f2cc60;
}

.github-dark .hljs-emphasis {
  /* prettylights-syntax-markup-italic */
  color: #c9d1d9;
  font-style: italic;
}

.github-dark .hljs-strong {
  /* prettylights-syntax-markup-bold */
  color: #c9d1d9;
  font-weight: bold;
}

.github-dark .hljs-addition {
  /* prettylights-syntax-markup-inserted */
  color: #aff5b4;
  background-color: #033a16;
}

.github-dark .hljs-deletion {
  /* prettylights-syntax-markup-deleted */
  color: #ffdcd7;
  background-color: #67060c;
}

.github-dark .hljs-char.escape_,
.github-dark .hljs-link,
.github-dark .hljs-params,
.github-dark .hljs-property,
.github-dark .hljs-punctuation,
.github-dark .hljs-tag {
  /* purposely ignored */
}
