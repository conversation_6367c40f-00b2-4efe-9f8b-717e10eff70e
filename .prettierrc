{"tabWidth": 2, "useTabs": false, "endOfLine": "lf", "semi": true, "singleQuote": false, "printWidth": 80, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "overrides": [{"files": ["*.js", "*.mjs", "*.jsx"], "options": {"parser": "flow", "arrowParens": "always"}}, {"files": ["*.config.js"], "options": {"semi": false, "parser": "flow", "trailingComma": "none"}}, {"files": "*.html", "options": {"bracketSameLine": true}}, {"files": ".prettier<PERSON>", "options": {"parser": "json"}}]}