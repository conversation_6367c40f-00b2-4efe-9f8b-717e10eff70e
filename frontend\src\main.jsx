import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON>rowser<PERSON>outer as Router } from "react-router-dom";
import App from "@/App.jsx";
import "@/index.css";
const isDev = process.env.NODE_ENV !== "production";
const REACTWRAP = isDev ? React.Fragment : React.StrictMode;

ReactDOM.createRoot(document.getElementById("root")).render(
  <REACTWRAP>
    <Router>
      <App />
    </Router>
  </REACTWRAP>
);
